import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import {
  createSupabaseClient,
  corsHeaders,
  errorResponse,
  successResponse,
  validationErrorResponse,
  handleCors,
  getUserFromToken,
  checkUserRole,
  validateBusinessNumber,
  validateVatId,
  validateEmail,
  validateIsraeliPhone,
  logAudit,
} from '../_shared/utils.ts';
import { CreateCustomerRequest, ValidationError } from '../_shared/types.ts';

serve(async (req) => {
  // Handle CORS
  const corsResponse = handleCors(req);
  if (corsResponse) return corsResponse;

  try {
    if (req.method !== 'POST') {
      return errorResponse('Method not allowed', 405);
    }

    // Get user from token
    const user = await getUserFromToken(req);
    
    const body: CreateCustomerRequest = await req.json();
    
    // Validate input
    const validationErrors: ValidationError[] = [];

    if (!body.company_id) {
      validationErrors.push({ field: 'company_id', message: 'Company ID is required' });
    }

    if (!body.business_number || !validateBusinessNumber(body.business_number)) {
      validationErrors.push({ field: 'business_number', message: 'Valid 9-digit business number is required' });
    }

    if (!body.name_hebrew || body.name_hebrew.trim().length < 2) {
      validationErrors.push({ field: 'name_hebrew', message: 'Customer name in Hebrew is required' });
    }

    if (body.vat_id && !validateVatId(body.vat_id)) {
      validationErrors.push({ field: 'vat_id', message: 'VAT ID must be 9 digits if provided' });
    }

    if (!body.billing_address_hebrew || body.billing_address_hebrew.trim().length < 5) {
      validationErrors.push({ field: 'billing_address_hebrew', message: 'Billing address in Hebrew is required' });
    }

    if (!body.city_hebrew || body.city_hebrew.trim().length < 2) {
      validationErrors.push({ field: 'city_hebrew', message: 'City in Hebrew is required' });
    }

    if (body.contact_email && !validateEmail(body.contact_email)) {
      validationErrors.push({ field: 'contact_email', message: 'Valid email format required' });
    }

    if (body.contact_phone && !validateIsraeliPhone(body.contact_phone)) {
      validationErrors.push({ field: 'contact_phone', message: 'Valid Israeli phone number required' });
    }

    if (validationErrors.length > 0) {
      return validationErrorResponse(validationErrors);
    }

    // Check user access to company
    await checkUserRole(user.id, body.company_id, ['admin', 'user', 'accountant']);

    const supabase = createSupabaseClient();

    // Check if customer with same business number already exists for this company
    const { data: existingCustomer } = await supabase
      .from('customers')
      .select('id')
      .eq('company_id', body.company_id)
      .eq('business_number', body.business_number)
      .single();

    if (existingCustomer) {
      return errorResponse('Customer with this business number already exists', 409);
    }

    // Create customer
    const { data: customer, error: customerError } = await supabase
      .from('customers')
      .insert({
        company_id: body.company_id,
        business_number: body.business_number,
        name_hebrew: body.name_hebrew.trim(),
        name_english: body.name_english?.trim(),
        vat_id: body.vat_id,
        billing_address_hebrew: body.billing_address_hebrew.trim(),
        billing_address_english: body.billing_address_english?.trim(),
        shipping_address_hebrew: body.shipping_address_hebrew?.trim(),
        shipping_address_english: body.shipping_address_english?.trim(),
        city_hebrew: body.city_hebrew.trim(),
        city_english: body.city_english?.trim(),
        contact_name: body.contact_name?.trim(),
        contact_email: body.contact_email?.trim(),
        contact_phone: body.contact_phone?.trim(),
        notes: body.notes?.trim(),
      })
      .select()
      .single();

    if (customerError) {
      console.error('Customer creation error:', customerError);
      return errorResponse('Failed to create customer', 500);
    }

    // Log audit
    await logAudit(
      body.company_id,
      user.id,
      'create',
      'customer',
      customer.id,
      null,
      customer,
      req
    );

    // Return response in the format expected by iOS app
    return new Response(
      JSON.stringify({
        success: true,
        data: {
          customer: customer
        },
        error: null
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );

  } catch (error) {
    console.error('Unexpected error:', error);
    if (error.message.includes('No access') || error.message.includes('Insufficient')) {
      return errorResponse(error.message, 403);
    }
    if (error.message.includes('Invalid token')) {
      return errorResponse('Unauthorized', 401);
    }
    return errorResponse('Internal server error', 500);
  }
});
