import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import {
  createSupabaseClient,
  corsHeaders,
  errorResponse,
  successResponse,
  validationErrorResponse,
  handleCors,
  getUserFromToken,
  checkUserRole,
  logAudit,
} from '../_shared/utils.ts';
import { ValidationError } from '../_shared/types.ts';

interface DeleteCustomerRequest {
  customer_id: string;
}

serve(async (req) => {
  // Handle CORS
  const corsResponse = handleCors(req);
  if (corsResponse) return corsResponse;

  try {
    if (req.method !== 'DELETE') {
      return errorResponse('Method not allowed', 405);
    }

    // Get user from token
    const user = await getUserFromToken(req);
    
    const body: DeleteCustomerRequest = await req.json();
    
    // Validate input
    const validationErrors: ValidationError[] = [];

    if (!body.customer_id) {
      validationErrors.push({ field: 'customer_id', message: 'Customer ID is required' });
    }

    if (validationErrors.length > 0) {
      return validationErrorResponse(validationErrors);
    }

    const supabase = createSupabaseClient();

    // Get existing customer to verify ownership and get company_id
    const { data: existingCustomer, error: fetchError } = await supabase
      .from('customers')
      .select('*')
      .eq('id', body.customer_id)
      .single();

    if (fetchError || !existingCustomer) {
      console.error('Customer fetch error:', fetchError);
      return errorResponse('Customer not found', 404);
    }

    // Check user access to company
    await checkUserRole(user.id, existingCustomer.company_id, ['admin', 'user', 'accountant']);

    // Check if customer has any documents (invoices, receipts, etc.)
    const { data: documents, error: documentsError } = await supabase
      .from('documents')
      .select('id')
      .eq('customer_id', body.customer_id)
      .limit(1);

    if (documentsError) {
      console.error('Documents check error:', documentsError);
      return errorResponse('Failed to check customer documents', 500);
    }

    if (documents && documents.length > 0) {
      return errorResponse('Cannot delete customer with existing documents. Please delete or reassign documents first.', 409);
    }

    // Delete customer
    const { error: deleteError } = await supabase
      .from('customers')
      .delete()
      .eq('id', body.customer_id);

    if (deleteError) {
      console.error('Customer delete error:', deleteError);
      return errorResponse('Failed to delete customer', 500);
    }

    // Log audit
    await logAudit(
      existingCustomer.company_id,
      user.id,
      'delete',
      'customer',
      body.customer_id,
      existingCustomer,
      null,
      req
    );

    // Return response in the format expected by iOS app
    return new Response(
      JSON.stringify({
        success: true,
        data: {
          customer_id: body.customer_id
        },
        error: null
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );

  } catch (error) {
    console.error('Delete customer error:', error);
    return errorResponse('Internal server error', 500);
  }
});
