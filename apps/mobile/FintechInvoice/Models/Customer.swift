import Foundation
import SwiftUI

// MARK: - Customer Model
struct Customer: Identifiable, Codable {
    let id: String
    let companyId: String
    let businessNumber: String
    let nameHebrew: String
    let nameEnglish: String?
    let vatId: String?
    let billingAddressHebrew: String
    let billingAddressEnglish: String?
    let shippingAddressHebrew: String?
    let shippingAddressEnglish: String?
    let cityHebrew: String
    let cityEnglish: String?
    let contactName: String?
    let contactEmail: String?
    let contactPhone: String?
    let notes: String?
    let createdAt: String
    let updatedAt: String
    
    // Computed properties for display
    var displayName: String {
        return nameHebrew
    }
    
    var fullAddress: String {
        var address = billingAddressHebrew
        if !cityHebrew.isEmpty {
            address += ", " + cityHebrew
        }
        return address
    }
    
    var hasContactInfo: Bool {
        return contactEmail != nil || contactPhone != nil
    }
    
    var formattedBusinessNumber: String {
        // Format business number with proper spacing (e.g., 123-456-789)
        let cleaned = businessNumber.replacingOccurrences(of: "[^0-9]", with: "", options: .regularExpression)
        if cleaned.count == 9 {
            let index1 = cleaned.index(cleaned.startIndex, offsetBy: 3)
            let index2 = cleaned.index(cleaned.startIndex, offsetBy: 6)
            return "\(cleaned[..<index1])-\(cleaned[index1..<index2])-\(cleaned[index2...])"
        }
        return businessNumber
    }
    
    var formattedVatId: String? {
        guard let vatId = vatId else { return nil }
        // Format VAT ID with proper spacing (e.g., 123-456-789)
        let cleaned = vatId.replacingOccurrences(of: "[^0-9]", with: "", options: .regularExpression)
        if cleaned.count == 9 {
            let index1 = cleaned.index(cleaned.startIndex, offsetBy: 3)
            let index2 = cleaned.index(cleaned.startIndex, offsetBy: 6)
            return "\(cleaned[..<index1])-\(cleaned[index1..<index2])-\(cleaned[index2...])"
        }
        return vatId
    }
    
    // Coding keys to map Swift property names to database field names
    enum CodingKeys: String, CodingKey {
        case id
        case companyId = "company_id"
        case businessNumber = "business_number"
        case nameHebrew = "name_hebrew"
        case nameEnglish = "name_english"
        case vatId = "vat_id"
        case billingAddressHebrew = "billing_address_hebrew"
        case billingAddressEnglish = "billing_address_english"
        case shippingAddressHebrew = "shipping_address_hebrew"
        case shippingAddressEnglish = "shipping_address_english"
        case cityHebrew = "city_hebrew"
        case cityEnglish = "city_english"
        case contactName = "contact_name"
        case contactEmail = "contact_email"
        case contactPhone = "contact_phone"
        case notes
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

// MARK: - Customer Creation Request
struct CreateCustomerRequest: Codable {
    let companyId: String
    let businessNumber: String
    let nameHebrew: String
    let nameEnglish: String?
    let vatId: String?
    let billingAddressHebrew: String
    let cityHebrew: String
    let contactEmail: String?
    let contactPhone: String?
    let notes: String?

    enum CodingKeys: String, CodingKey {
        case companyId = "company_id"
        case businessNumber = "business_number"
        case nameHebrew = "name_hebrew"
        case nameEnglish = "name_english"
        case vatId = "vat_id"
        case billingAddressHebrew = "billing_address_hebrew"
        case cityHebrew = "city_hebrew"
        case contactEmail = "contact_email"
        case contactPhone = "contact_phone"
        case notes
    }
}

// MARK: - Customer Update Request
struct UpdateCustomerRequest: Codable {
    let customerId: String
    let businessNumber: String?
    let nameHebrew: String?
    let nameEnglish: String?
    let vatId: String?
    let billingAddressHebrew: String?
    let cityHebrew: String?
    let contactEmail: String?
    let contactPhone: String?
    let notes: String?

    enum CodingKeys: String, CodingKey {
        case customerId = "customer_id"
        case businessNumber = "business_number"
        case nameHebrew = "name_hebrew"
        case nameEnglish = "name_english"
        case vatId = "vat_id"
        case billingAddressHebrew = "billing_address_hebrew"
        case cityHebrew = "city_hebrew"
        case contactEmail = "contact_email"
        case contactPhone = "contact_phone"
        case notes
    }
}

// MARK: - Customer Delete Request
struct DeleteCustomerRequest: Codable {
    let customerId: String

    enum CodingKeys: String, CodingKey {
        case customerId = "customer_id"
    }
}

// MARK: - Customer Response Types
struct CustomerResponse: Codable {
    let success: Bool
    let data: CustomerResponseData?
    let error: String?
}

struct CustomerResponseData: Codable {
    let customer: Customer
}

struct CustomersListResponse: Codable {
    let success: Bool
    let data: CustomersListData?
    let error: String?
}

struct CustomersListData: Codable {
    let customers: [Customer]
    let total: Int?
}

struct CustomerSearchResponse: Codable {
    let success: Bool
    let data: CustomerSearchData?
    let error: String?
}

struct CustomerSearchData: Codable {
    let customers: [Customer]
}

// MARK: - Customer Delete Response
struct CustomerDeleteResponse: Codable {
    let success: Bool
    let data: CustomerDeleteData?
    let error: String?
}

struct CustomerDeleteData: Codable {
    let customerId: String

    enum CodingKeys: String, CodingKey {
        case customerId = "customer_id"
    }
}

// MARK: - Customer Form Data (for UI)
class CustomerFormData: ObservableObject {
    @Published var nameHebrew: String = ""
    @Published var businessNumber: String = ""
    @Published var billingAddressHebrew: String = ""
    @Published var cityHebrew: String = ""
    @Published var contactEmail: String = ""
    @Published var contactPhone: String = ""
    @Published var notes: String = ""
    
    var isValid: Bool {
        return !nameHebrew.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
               !businessNumber.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
               !billingAddressHebrew.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
               !cityHebrew.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
    
    var businessNumberError: String? {
        let cleaned = businessNumber.replacingOccurrences(of: "[^0-9]", with: "", options: .regularExpression)
        if !businessNumber.isEmpty && cleaned.count != 9 {
            return "מספר עסק חייב להכיל 9 ספרות"
        }
        return nil
    }
    

    
    var emailError: String? {
        if !contactEmail.isEmpty {
            let emailRegex = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
            let emailPredicate = NSPredicate(format:"SELF MATCHES %@", emailRegex)
            if !emailPredicate.evaluate(with: contactEmail) {
                return "כתובת אימייל לא תקינה"
            }
        }
        return nil
    }
    
    var phoneError: String? {
        if !contactPhone.isEmpty {
            let cleaned = contactPhone.replacingOccurrences(of: "[^0-9]", with: "", options: .regularExpression)
            if cleaned.count < 9 || cleaned.count > 10 {
                return "מספר טלפון לא תקין"
            }
        }
        return nil
    }
    
    func toCreateRequest(companyId: String) -> CreateCustomerRequest {
        let cleanedBusinessNumber = businessNumber.replacingOccurrences(of: "[^0-9]", with: "", options: .regularExpression)
        return CreateCustomerRequest(
            companyId: companyId,
            businessNumber: cleanedBusinessNumber,
            nameHebrew: nameHebrew.trimmingCharacters(in: .whitespacesAndNewlines),
            nameEnglish: nil,
            vatId: cleanedBusinessNumber.isEmpty ? nil : cleanedBusinessNumber,
            billingAddressHebrew: billingAddressHebrew.trimmingCharacters(in: .whitespacesAndNewlines),
            cityHebrew: cityHebrew.trimmingCharacters(in: .whitespacesAndNewlines),
            contactEmail: contactEmail.isEmpty ? nil : contactEmail.trimmingCharacters(in: .whitespacesAndNewlines),
            contactPhone: contactPhone.isEmpty ? nil : contactPhone.replacingOccurrences(of: "[^0-9]", with: "", options: .regularExpression),
            notes: notes.isEmpty ? nil : notes.trimmingCharacters(in: .whitespacesAndNewlines)
        )
    }

    func toUpdateRequest(customerId: String) -> UpdateCustomerRequest {
        let cleanedBusinessNumber = businessNumber.replacingOccurrences(of: "[^0-9]", with: "", options: .regularExpression)
        return UpdateCustomerRequest(
            customerId: customerId,
            businessNumber: cleanedBusinessNumber,
            nameHebrew: nameHebrew.trimmingCharacters(in: .whitespacesAndNewlines),
            nameEnglish: nil,
            vatId: cleanedBusinessNumber.isEmpty ? nil : cleanedBusinessNumber,
            billingAddressHebrew: billingAddressHebrew.trimmingCharacters(in: .whitespacesAndNewlines),
            cityHebrew: cityHebrew.trimmingCharacters(in: .whitespacesAndNewlines),
            contactEmail: contactEmail.isEmpty ? nil : contactEmail.trimmingCharacters(in: .whitespacesAndNewlines),
            contactPhone: contactPhone.isEmpty ? nil : contactPhone.replacingOccurrences(of: "[^0-9]", with: "", options: .regularExpression),
            notes: notes.isEmpty ? nil : notes.trimmingCharacters(in: .whitespacesAndNewlines)
        )
    }

    func populateFromCustomer(_ customer: Customer) {
        nameHebrew = customer.nameHebrew
        businessNumber = customer.businessNumber
        billingAddressHebrew = customer.billingAddressHebrew
        cityHebrew = customer.cityHebrew
        contactEmail = customer.contactEmail ?? ""
        contactPhone = customer.contactPhone ?? ""
        notes = customer.notes ?? ""
    }

    func reset() {
        nameHebrew = ""
        businessNumber = ""
        billingAddressHebrew = ""
        cityHebrew = ""
        contactEmail = ""
        contactPhone = ""
        notes = ""
    }
}
