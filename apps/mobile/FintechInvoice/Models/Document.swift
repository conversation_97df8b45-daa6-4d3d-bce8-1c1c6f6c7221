import Foundation

// MARK: - Document Types and Status Enums
enum DocumentType: String, CaseIterable, Codable {
    case taxInvoice = "tax_invoice"
    case receipt = "receipt"
    case creditNote = "credit_note"
    case taxInvoiceReceipt = "tax_invoice_receipt"
    
    var displayName: String {
        switch self {
        case .taxInvoice:
            return "חשבונית מס"
        case .receipt:
            return "קבלה"
        case .creditNote:
            return "זיכוי"
        case .taxInvoiceReceipt:
            return "חשבונית מס/קבלה"
        }
    }
    
    var icon: String {
        switch self {
        case .taxInvoice:
            return "doc.text"
        case .receipt:
            return "receipt"
        case .creditNote:
            return "minus.circle"
        case .taxInvoiceReceipt:
            return "doc.text.fill"
        }
    }
}

enum DocumentStatus: String, CaseIterable, Codable {
    case draft = "draft"
    case pendingAllocation = "pending_allocation"
    case approved = "approved"
    case sent = "sent"
    case paid = "paid"
    case cancelled = "cancelled"
    
    var displayName: String {
        switch self {
        case .draft:
            return "טיוטה"
        case .pendingAllocation:
            return "ממתין להקצאה"
        case .approved:
            return "מאושר"
        case .sent:
            return "נשלח"
        case .paid:
            return "שולם"
        case .cancelled:
            return "בוטל"
        }
    }
    
    var color: String {
        switch self {
        case .draft:
            return "gray"
        case .pendingAllocation:
            return "orange"
        case .approved:
            return "blue"
        case .sent:
            return "purple"
        case .paid:
            return "green"
        case .cancelled:
            return "red"
        }
    }
}

// MARK: - Document Model
struct Document: Identifiable, Codable {
    let id: String
    let companyId: String
    let documentType: DocumentType
    let documentNumber: String
    let customerId: String
    let issueDate: String
    let dueDate: String?
    let currency: String?
    let subtotal: Double
    let vatAmount: Double
    let totalAmount: Double
    let status: DocumentStatus
    let itaAllocationNumber: String?
    let itaSubmittedAt: String?
    let itaSubmissionAttempts: Int?
    let itaLastError: String?
    let parentDocumentId: String?
    let notes: String?
    let templateId: String?
    let pdfUrl: String?
    let sentAt: String?
    let sentVia: String?
    let createdBy: String
    let createdAt: String?
    let updatedAt: String?

    // Custom decoder to handle string-to-number conversion
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        id = try container.decode(String.self, forKey: .id)
        companyId = try container.decode(String.self, forKey: .companyId)
        documentType = try container.decode(DocumentType.self, forKey: .documentType)
        documentNumber = try container.decode(String.self, forKey: .documentNumber)
        customerId = try container.decode(String.self, forKey: .customerId)
        issueDate = try container.decode(String.self, forKey: .issueDate)
        dueDate = try container.decodeIfPresent(String.self, forKey: .dueDate)
        currency = try container.decodeIfPresent(String.self, forKey: .currency)

        // Handle numeric fields that might come as strings
        if let subtotalString = try? container.decode(String.self, forKey: .subtotal) {
            subtotal = Double(subtotalString) ?? 0.0
        } else {
            subtotal = try container.decode(Double.self, forKey: .subtotal)
        }

        if let vatAmountString = try? container.decode(String.self, forKey: .vatAmount) {
            vatAmount = Double(vatAmountString) ?? 0.0
        } else {
            vatAmount = try container.decode(Double.self, forKey: .vatAmount)
        }

        if let totalAmountString = try? container.decode(String.self, forKey: .totalAmount) {
            totalAmount = Double(totalAmountString) ?? 0.0
        } else {
            totalAmount = try container.decode(Double.self, forKey: .totalAmount)
        }

        status = try container.decode(DocumentStatus.self, forKey: .status)
        itaAllocationNumber = try container.decodeIfPresent(String.self, forKey: .itaAllocationNumber)
        itaSubmittedAt = try container.decodeIfPresent(String.self, forKey: .itaSubmittedAt)
        itaSubmissionAttempts = try container.decodeIfPresent(Int.self, forKey: .itaSubmissionAttempts) ?? 0
        itaLastError = try container.decodeIfPresent(String.self, forKey: .itaLastError)
        parentDocumentId = try container.decodeIfPresent(String.self, forKey: .parentDocumentId)
        notes = try container.decodeIfPresent(String.self, forKey: .notes)
        templateId = try container.decodeIfPresent(String.self, forKey: .templateId)
        pdfUrl = try container.decodeIfPresent(String.self, forKey: .pdfUrl)
        sentAt = try container.decodeIfPresent(String.self, forKey: .sentAt)
        sentVia = try container.decodeIfPresent(String.self, forKey: .sentVia)
        createdBy = try container.decode(String.self, forKey: .createdBy)
        createdAt = try container.decodeIfPresent(String.self, forKey: .createdAt)
        updatedAt = try container.decodeIfPresent(String.self, forKey: .updatedAt)
    }

    // Custom encoder to maintain compatibility
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)

        try container.encode(id, forKey: .id)
        try container.encode(companyId, forKey: .companyId)
        try container.encode(documentType, forKey: .documentType)
        try container.encode(documentNumber, forKey: .documentNumber)
        try container.encode(customerId, forKey: .customerId)
        try container.encode(issueDate, forKey: .issueDate)
        try container.encodeIfPresent(dueDate, forKey: .dueDate)
        try container.encodeIfPresent(currency, forKey: .currency)
        try container.encode(subtotal, forKey: .subtotal)
        try container.encode(vatAmount, forKey: .vatAmount)
        try container.encode(totalAmount, forKey: .totalAmount)
        try container.encode(status, forKey: .status)
        try container.encodeIfPresent(itaAllocationNumber, forKey: .itaAllocationNumber)
        try container.encodeIfPresent(itaSubmittedAt, forKey: .itaSubmittedAt)
        try container.encodeIfPresent(itaSubmissionAttempts, forKey: .itaSubmissionAttempts)
        try container.encodeIfPresent(itaLastError, forKey: .itaLastError)
        try container.encodeIfPresent(parentDocumentId, forKey: .parentDocumentId)
        try container.encodeIfPresent(notes, forKey: .notes)
        try container.encodeIfPresent(templateId, forKey: .templateId)
        try container.encodeIfPresent(pdfUrl, forKey: .pdfUrl)
        try container.encodeIfPresent(sentAt, forKey: .sentAt)
        try container.encodeIfPresent(sentVia, forKey: .sentVia)
        try container.encode(createdBy, forKey: .createdBy)
        try container.encodeIfPresent(createdAt, forKey: .createdAt)
        try container.encodeIfPresent(updatedAt, forKey: .updatedAt)
    }
    
    // Computed properties for display
    var formattedTotal: String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = currency
        formatter.currencySymbol = "₪"
        return formatter.string(from: NSNumber(value: totalAmount)) ?? "₪\(totalAmount)"
    }
    
    var formattedSubtotal: String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = currency
        formatter.currencySymbol = "₪"
        return formatter.string(from: NSNumber(value: subtotal)) ?? "₪\(subtotal)"
    }
    
    var formattedVat: String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = currency
        formatter.currencySymbol = "₪"
        return formatter.string(from: NSNumber(value: vatAmount)) ?? "₪\(vatAmount)"
    }
    
    var formattedIssueDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.locale = Locale(identifier: "he_IL")
        
        let isoFormatter = DateFormatter()
        isoFormatter.dateFormat = "yyyy-MM-dd"
        
        if let date = isoFormatter.date(from: issueDate) {
            return formatter.string(from: date)
        }
        return issueDate
    }
    
    var formattedDueDate: String? {
        guard let dueDate = dueDate else { return nil }
        
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.locale = Locale(identifier: "he_IL")
        
        let isoFormatter = DateFormatter()
        isoFormatter.dateFormat = "yyyy-MM-dd"
        
        if let date = isoFormatter.date(from: dueDate) {
            return formatter.string(from: date)
        }
        return dueDate
    }
    
    // Coding keys to map Swift property names to database field names
    enum CodingKeys: String, CodingKey {
        case id
        case companyId = "company_id"
        case documentType = "document_type"
        case documentNumber = "document_number"
        case customerId = "customer_id"
        case issueDate = "issue_date"
        case dueDate = "due_date"
        case currency
        case subtotal
        case vatAmount = "vat_amount"
        case totalAmount = "total_amount"
        case status
        case itaAllocationNumber = "ita_allocation_number"
        case itaSubmittedAt = "ita_allocation_date"
        case itaSubmissionAttempts = "ita_submission_attempts"
        case itaLastError = "ita_last_error"
        case parentDocumentId = "parent_document_id"
        case notes
        case templateId = "template_id"
        case pdfUrl = "pdf_url"
        case sentAt = "sent_at"
        case sentVia = "sent_via"
        case createdBy = "created_by"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

// MARK: - Document Creation Request
struct CreateDocumentRequest: Codable {
    let companyId: String
    let documentType: DocumentType
    let customerId: String
    let issueDate: String
    let dueDate: String?
    let currency: String
    let items: [CreateDocumentItemRequest]
    let notes: String?
    let templateId: String?
    let status: DocumentStatus

    enum CodingKeys: String, CodingKey {
        case companyId = "company_id"
        case documentType = "document_type"
        case customerId = "customer_id"
        case issueDate = "issue_date"
        case dueDate = "due_date"
        case currency
        case items
        case notes
        case templateId = "template_id"
        case status
    }
}

// MARK: - Document Update Request
struct UpdateDocumentRequest: Codable {
    let documentId: String
    let documentType: DocumentType?
    let customerId: String?
    let issueDate: String?
    let dueDate: String?
    let currency: String?
    let items: [CreateDocumentItemRequest]?
    let notes: String?
    let templateId: String?
    let status: DocumentStatus?

    enum CodingKeys: String, CodingKey {
        case documentId = "document_id"
        case documentType = "document_type"
        case customerId = "customer_id"
        case issueDate = "issue_date"
        case dueDate = "due_date"
        case currency
        case items
        case notes
        case templateId = "template_id"
        case status
    }
}

struct CreateDocumentItemRequest: Codable {
    let productId: String?
    let descriptionHebrew: String
    let descriptionEnglish: String?
    let quantity: Double
    let unitPrice: Double
    let vatRate: Double
    let discountPercent: Double?
    
    enum CodingKeys: String, CodingKey {
        case productId = "product_id"
        case descriptionHebrew = "description_hebrew"
        case descriptionEnglish = "description_english"
        case quantity
        case unitPrice = "unit_price"
        case vatRate = "vat_rate"
        case discountPercent = "discount_percent"
    }
}

// MARK: - Document Response Types
struct DocumentResponse: Codable {
    let success: Bool
    let data: DocumentResponseData?
    let error: String?
}

struct DocumentResponseData: Codable {
    let document: Document
    let itaSubmission: ITASubmissionInfo?

    enum CodingKeys: String, CodingKey {
        case document
        case itaSubmission = "ita_submission"
    }
}

struct ITASubmissionInfo: Codable {
    let status: String
    let message: String
    let allocationNumber: String?
    let error: String?

    enum CodingKeys: String, CodingKey {
        case status
        case message
        case allocationNumber = "allocation_number"
        case error
    }
}

struct NextDocumentNumberResponse: Codable {
    let success: Bool
    let data: NextDocumentNumberData?
    let error: String?
}

struct NextDocumentNumberData: Codable {
    let nextNumber: String
    let prefix: String
    let sequence: Int
    
    enum CodingKeys: String, CodingKey {
        case nextNumber = "next_number"
        case prefix
        case sequence
    }
}
