import Foundation
import Supabase
import Auth
import PostgREST

// MARK: - Data Models

// Registration response from auth-register edge function
struct RegisterResponse: Codable {
    let user: RegisteredUser
    let company: CompanyRecord
    let verification_email_sent: Bool

    struct RegisteredUser: Codable {
        let id: String
        let email: String
        let full_name: String
        let phone: String
    }
}

// Registration data structure
struct RegistrationData {
    let email: String
    let password: String
    let fullName: String
    let phone: String
    let companyId: String
    let name: String // Hebrew name as primary name
    let nameEnglish: String?
    let addressHebrew: String
    let cityHebrew: String
    let industry: String
    let annualRevenue: String
    let interestedInLoan: Bool
    let interestedInInsurance: Bool
    let interestedInAccounting: Bool
}

// Registration request structure for edge function
struct RegistrationRequest: Codable {
    let email: String
    let password: String
    let full_name: String
    let phone: String
    let company: CompanyRequest

    struct CompanyRequest: Codable {
        let business_number: String
        let name: String // Hebrew name as primary name
        let name_english: String
        let vat_id: String
        let address_hebrew: String
        let city_hebrew: String
        let phone: String
        let industry: String
        let annual_revenue: String
        let interested_in_loan: Bool
        let interested_in_insurance: Bool
        let interested_in_accounting: Bool
    }
}

// Edge function response wrapper
struct EdgeFunctionResponse: Codable {
    let success: Bool
    let data: RegisterResponse?
    let message: String?
    let error: String?
}

struct CompanyRecord: Codable {
    let id: String?
    let businessNumber: String
    let name: String // Hebrew name as primary name
    let nameEnglish: String?
    let addressHebrew: String?  // Made optional since not always selected
    let cityHebrew: String?     // Made optional since not always selected
    let phone: String?          // Made optional since not always selected
    let industry: String?       // Made optional since not always selected
    let annualRevenue: String?  // Made optional since not always selected
    let interestedInLoan: Bool? // Made optional since not always selected
    let interestedInInsurance: Bool? // Made optional since not always selected
    let interestedInAccounting: Bool? // Made optional since not always selected
    let subscriptionTier: String?
    let createdAt: String?
    let updatedAt: String?

    enum CodingKeys: String, CodingKey {
        case id
        case businessNumber = "business_number"
        case name = "name" // Hebrew name as primary name
        case nameEnglish = "name_english"
        case addressHebrew = "address_hebrew"
        case cityHebrew = "city_hebrew"
        case phone
        case industry
        case annualRevenue = "annual_revenue"
        case interestedInLoan = "interested_in_loan"
        case interestedInInsurance = "interested_in_insurance"
        case interestedInAccounting = "interested_in_accounting"
        case subscriptionTier = "subscription_tier"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

struct UserRecord: Codable {
    let id: String
    let email: String
    let fullName: String
    let phone: String
    let createdAt: String?
    let updatedAt: String?

    enum CodingKeys: String, CodingKey {
        case id
        case email
        case fullName = "full_name"
        case phone
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

struct CompanyUserRecord: Codable {
    let id: String?
    let companyId: String
    let userId: String
    let role: String
    let createdBy: String
    let createdAt: String?

    enum CodingKeys: String, CodingKey {
        case id
        case companyId = "company_id"
        case userId = "user_id"
        case role
        case createdBy = "created_by"
        case createdAt = "created_at"
    }
}

struct CompanyUserWithCompany: Codable {
    let companyId: String
    let role: String
    let companies: CompanyRecord

    enum CodingKeys: String, CodingKey {
        case companyId = "company_id"
        case role
        case companies
    }
}

class SupabaseService: ObservableObject, @unchecked Sendable {
    nonisolated(unsafe) static let shared = SupabaseService()

    private var client: SupabaseClient?

    private init() {}

    private var safeClient: SupabaseClient {
        guard let client = client else {
            fatalError("SupabaseService not configured. Call configure() first.")
        }
        return client
    }

    func configure() {
        // Get Supabase credentials from environment or configuration
        guard let supabaseURL = getSupabaseURL(),
              let supabaseKey = getSupabaseAnonKey() else {
            print("❌ Supabase configuration missing. URL: \(getSupabaseURL() != nil ? "✓" : "✗"), Key: \(getSupabaseAnonKey() != nil ? "✓" : "✗")")
            fatalError("Supabase configuration missing. Please check your environment variables.")
        }

        print("🔧 Configuring Supabase with URL: \(supabaseURL)")
        client = SupabaseClient(
            supabaseURL: supabaseURL,
            supabaseKey: supabaseKey
        )
        print("✅ Supabase client configured successfully")
    }
    
    // MARK: - Authentication

    func signUp(registrationData: RegistrationData) async throws -> RegisterResponse {
        print("🔐 Starting user registration for email: \(registrationData.email)")

        // Prepare registration request for edge function
        let requestBody = RegistrationRequest(
            email: registrationData.email,
            password: registrationData.password,
            full_name: registrationData.fullName,
            phone: registrationData.phone,
            company: RegistrationRequest.CompanyRequest(
                business_number: registrationData.companyId,
                name: registrationData.name,
                name_english: registrationData.nameEnglish ?? "",
                vat_id: registrationData.companyId, // Same as business number in Israel
                address_hebrew: registrationData.addressHebrew,
                city_hebrew: registrationData.cityHebrew,
                phone: registrationData.phone,
                industry: registrationData.industry,
                annual_revenue: registrationData.annualRevenue,
                interested_in_loan: registrationData.interestedInLoan,
                interested_in_insurance: registrationData.interestedInInsurance,
                interested_in_accounting: registrationData.interestedInAccounting
            )
        )

        // Call the auth-register edge function
        let responseData: Data = try await safeClient.functions
            .invoke("auth-register", options: FunctionInvokeOptions(
                body: try JSONEncoder().encode(requestBody)
            ))

        let functionResponse = try JSONDecoder().decode(EdgeFunctionResponse.self, from: responseData)

        guard functionResponse.success, let response = functionResponse.data else {
            print("❌ Registration failed: \(functionResponse.error ?? "Unknown error")")
            throw SupabaseError.networkError
        }

        print("✅ Registration successful for user: \(registrationData.email)")
        print("📧 Email verification required - user must confirm email before signing in")

        return response
    }

    func signIn(email: String, password: String) async throws -> Session {
        print("🔐 Starting user signin for email: \(email)")
        let session = try await safeClient.auth.signIn(
            email: email,
            password: password
        )
        print("✅ Signin successful for user: \(session.user.email ?? "unknown")")
        return session
    }

    func signOut() async throws {
        print("🔐 Starting user signout")
        try await safeClient.auth.signOut()
        print("✅ Signout successful")
    }

    func getCurrentUser() async throws -> User? {
        do {
            print("🔍 Getting current user session")
            let session = try await safeClient.auth.session
            print("✅ Current user found: \(session.user.email ?? "unknown")")
            return session.user
        } catch {
            print("⚠️ No valid session found: \(error.localizedDescription)")
            return nil
        }
    }

    func getCurrentSession() async throws -> Session {
        print("🔍 Getting current session")
        let session = try await safeClient.auth.session
        print("✅ Session retrieved for user: \(session.user.email ?? "unknown")")
        return session
    }

    func resendConfirmationEmail(email: String) async throws {
        print("📧 Resending confirmation email to: \(email)")
        try await safeClient.auth.resend(
            email: email,
            type: .signup
        )
        print("✅ Confirmation email resent successfully")
    }

    // MARK: - Database Operations

    func createCompany(_ companyData: CompanyRecord) async throws -> CompanyRecord {
        print("🏢 Creating company: \(companyData.name)")
        let response: CompanyRecord = try await safeClient.database
            .from("companies")
            .insert(companyData)
            .select()
            .single()
            .execute()
            .value
        print("✅ Company created with ID: \(response.id ?? "nil")")
        return response
    }

    func createUser(_ userData: UserRecord) async throws -> UserRecord {
        print("👤 Creating user record for: \(userData.email)")

        // Check if user is authenticated
        do {
            let session = try await safeClient.auth.session
            print("✅ User authenticated with ID: \(session.user.id)")
        } catch {
            print("❌ User not authenticated: \(error.localizedDescription)")
            throw SupabaseError.authenticationFailed
        }

        let response: UserRecord = try await safeClient.database
            .from("users")
            .insert(userData)
            .select()
            .single()
            .execute()
            .value
        print("✅ User record created with ID: \(response.id)")
        return response
    }

    func createCompanyUser(_ companyUserData: CompanyUserRecord) async throws -> CompanyUserRecord {
        print("🔗 Creating company-user relationship for user: \(companyUserData.userId)")
        let response: CompanyUserRecord = try await safeClient.database
            .from("company_users")
            .insert(companyUserData)
            .select()
            .single()
            .execute()
            .value
        print("✅ Company-user relationship created with ID: \(response.id ?? "nil")")
        return response
    }

    func getUserCompanies(userId: String) async throws -> [CompanyUserWithCompany] {
        print("🏢 Loading companies for user: \(userId)")
        let response: [CompanyUserWithCompany] = try await safeClient.database
            .from("company_users")
            .select("""
                company_id,
                role,
                companies (
                    id,
                    name,
                    name_english,
                    business_number,
                    subscription_tier
                )
            """)
            .eq("user_id", value: userId)
            .execute()
            .value
        print("✅ Loaded \(response.count) companies for user")
        return response
    }

    // MARK: - Document Operations

    func getNextDocumentNumber(companyId: String, documentType: DocumentType) async throws -> NextDocumentNumberData {
        print("📄 Getting next document number for type: \(documentType.rawValue)")

        let requestBody = [
            "company_id": companyId,
            "document_type": documentType.rawValue
        ]
        let response: Data = try await safeClient.functions.invoke(
            "documents-next-number",
            options: FunctionInvokeOptions(
                body: try JSONSerialization.data(withJSONObject: requestBody)
            )
        )

        let result = try JSONDecoder().decode(NextDocumentNumberResponse.self, from: response)

        if result.success, let data = result.data {
            print("✅ Next document number: \(data.nextNumber)")
            return data
        } else {
            print("❌ Failed to get next document number: \(result.error ?? "Unknown error")")
            throw SupabaseError.unknownError
        }
    }

    func createDocument(_ request: CreateDocumentRequest) async throws -> Document {
        print("📄 Creating document of type: \(request.documentType.rawValue)")

        let response: Data = try await safeClient.functions.invoke(
            "documents-create",
            options: FunctionInvokeOptions(
                body: try JSONEncoder().encode(request)
            )
        )

        // Debug: Print raw response
        if let responseString = String(data: response, encoding: .utf8) {
            print("🔍 Raw response: \(responseString)")
        }

        do {
            let result = try JSONDecoder().decode(DocumentResponse.self, from: response)

            if result.success, let data = result.data {
                print("✅ Document created with ID: \(data.document.id)")
                return data.document
            } else {
                print("❌ Failed to create document: \(result.error ?? "Unknown error")")
                throw SupabaseError.unknownError
            }
        } catch {
            print("❌ JSON Decoding Error: \(error)")
            if let decodingError = error as? DecodingError {
                print("❌ Detailed decoding error: \(decodingError)")
            }
            throw error
        }
    }

    func sendDocument(documentId: String, method: String) async throws {
        print("📧 Sending document \(documentId) via \(method)")

        let requestBody = [
            "document_id": documentId,
            "method": method,
            "recipient_email": "<EMAIL>" // Placeholder for now
        ]
        let response: Data = try await safeClient.functions.invoke(
            "documents-send",
            options: FunctionInvokeOptions(
                body: try JSONSerialization.data(withJSONObject: requestBody)
            )
        )

        // Debug: Print raw response
        if let responseString = String(data: response, encoding: .utf8) {
            print("🔍 Raw send response: \(responseString)")
        }

        do {
            // Parse response as JSON object
            let jsonObject = try JSONSerialization.jsonObject(with: response, options: []) as? [String: Any]

            if let success = jsonObject?["success"] as? Bool, success {
                print("✅ Document sent successfully")
            } else {
                let error = jsonObject?["error"] as? String ?? "Unknown error"
                print("❌ Failed to send document: \(error)")
                throw SupabaseError.unknownError
            }
        } catch {
            print("❌ Failed to parse send document response: \(error)")
            print("❌ Response data: \(String(data: response, encoding: .utf8) ?? "Unable to decode")")
            throw error
        }
    }

    // MARK: - Customer Operations

    func searchCustomers(companyId: String, query: String) async throws -> [Customer] {
        print("👥 Searching customers for query: \(query)")

        let requestBody = [
            "company_id": companyId,
            "query": query
        ]
        let response: Data = try await safeClient.functions.invoke(
            "customers-search",
            options: FunctionInvokeOptions(
                body: try JSONSerialization.data(withJSONObject: requestBody)
            )
        )

        let result = try JSONDecoder().decode(CustomerSearchResponse.self, from: response)

        if result.success, let data = result.data {
            print("✅ Found \(data.customers.count) customers")
            return data.customers
        } else {
            print("❌ Failed to search customers: \(result.error ?? "Unknown error")")
            throw SupabaseError.unknownError
        }
    }

    func getCustomers(companyId: String) async throws -> [Customer] {
        print("👥 Loading customers for company: \(companyId)")

        let response: [Customer] = try await safeClient.database
            .from("customers")
            .select("*")
            .eq("company_id", value: companyId)
            .order("name_hebrew")
            .execute()
            .value

        print("✅ Loaded \(response.count) customers")
        return response
    }

    func createCustomer(_ request: CreateCustomerRequest) async throws -> Customer {
        print("👥 Creating customer: \(request.nameHebrew)")

        do {
            // Try using the invoke method with explicit return type
            let functionResponse: CustomerResponse = try await safeClient.functions.invoke(
                "customers-create",
                options: FunctionInvokeOptions(
                    body: try JSONEncoder().encode(request)
                )
            )

            // Use the response directly
            let result = functionResponse

            if result.success, let data = result.data {
                print("✅ Customer created with ID: \(data.customer.id)")
                return data.customer
            } else {
                let errorMessage = result.error ?? "Unknown error"
                print("❌ Failed to create customer: \(errorMessage)")

                // Check for specific error types
                if errorMessage.contains("already exists") {
                    throw SupabaseError.customerAlreadyExists(request.businessNumber)
                }

                throw SupabaseError.serverError(errorMessage)
            }
        } catch let error as SupabaseError {
            // Re-throw SupabaseError as-is
            throw error
        } catch {
            print("❌ Network error creating customer: \(error)")
            throw SupabaseError.networkError
        }
    }

    func updateCustomer(_ request: UpdateCustomerRequest) async throws -> Customer {
        print("👥 Updating customer: \(request.customerId)")

        do {
            // Try using the invoke method with explicit return type
            let functionResponse: CustomerResponse = try await safeClient.functions.invoke(
                "customers-update",
                options: FunctionInvokeOptions(
                    body: try JSONEncoder().encode(request)
                )
            )

            // Use the response directly
            let result = functionResponse

            if result.success, let data = result.data {
                print("✅ Customer updated with ID: \(data.customer.id)")
                return data.customer
            } else {
                let errorMessage = result.error ?? "Unknown error"
                print("❌ Failed to update customer: \(errorMessage)")

                // Check for specific error types
                if errorMessage.contains("already exists") {
                    throw SupabaseError.customerAlreadyExists(request.businessNumber ?? "")
                }

                throw SupabaseError.serverError(errorMessage)
            }
        } catch let error as SupabaseError {
            // Re-throw SupabaseError as-is
            throw error
        } catch {
            print("❌ Network error updating customer: \(error)")
            throw SupabaseError.networkError
        }
    }

    func deleteCustomer(_ customerId: String) async throws {
        print("👥 Deleting customer: \(customerId)")

        do {
            let request = DeleteCustomerRequest(customerId: customerId)

            // Try using the invoke method with explicit return type
            let functionResponse: CustomerDeleteResponse = try await safeClient.functions.invoke(
                "customers-delete",
                options: FunctionInvokeOptions(
                    body: try JSONEncoder().encode(request)
                )
            )

            // Use the response directly
            let result = functionResponse

            if result.success {
                print("✅ Customer deleted with ID: \(customerId)")
            } else {
                let errorMessage = result.error ?? "Unknown error"
                print("❌ Failed to delete customer: \(errorMessage)")

                // Check for specific error types
                if errorMessage.contains("existing documents") {
                    throw SupabaseError.serverError("לא ניתן למחוק לקוח עם מסמכים קיימים")
                }

                throw SupabaseError.serverError(errorMessage)
            }
        } catch let error as SupabaseError {
            // Re-throw SupabaseError as-is
            throw error
        } catch {
            print("❌ Network error deleting customer: \(error)")
            throw SupabaseError.networkError
        }
    }

    // MARK: - Product Operations

    func getProducts(companyId: String) async throws -> [Product] {
        print("📦 Loading products for company: \(companyId)")

        let response: [Product] = try await safeClient.database
            .from("products")
            .select("*")
            .eq("company_id", value: companyId)
            .eq("is_active", value: true)
            .order("name_hebrew")
            .execute()
            .value

        print("✅ Loaded \(response.count) products")
        return response
    }

    // MARK: - Document Operations

    func getDocuments(companyId: String, limit: Int = 50) async throws -> [Document] {
        print("📄 Loading documents for company: \(companyId)")

        let response: [Document] = try await safeClient.database
            .from("documents")
            .select("*")
            .eq("company_id", value: companyId)
            .order("created_at", ascending: false)
            .limit(limit)
            .execute()
            .value

        print("✅ Loaded \(response.count) documents")
        return response
    }

    func getDocumentsByStatus(companyId: String, status: DocumentStatus, limit: Int = 50) async throws -> [Document] {
        print("📄 Loading documents for company: \(companyId) with status: \(status.rawValue)")

        let response: [Document] = try await safeClient.database
            .from("documents")
            .select("*")
            .eq("company_id", value: companyId)
            .eq("status", value: status.rawValue)
            .order("created_at", ascending: false)
            .limit(limit)
            .execute()
            .value

        print("✅ Loaded \(response.count) documents with status \(status.rawValue)")
        return response
    }

    func searchProducts(companyId: String, query: String) async throws -> [Product] {
        print("📦 Searching products for query: \(query)")

        let response: [Product] = try await safeClient.database
            .from("products")
            .select("*")
            .eq("company_id", value: companyId)
            .eq("is_active", value: true)
            .or("name_hebrew.ilike.%\(query)%,description_hebrew.ilike.%\(query)%")
            .order("name_hebrew")
            .execute()
            .value

        print("✅ Found \(response.count) products")
        return response
    }

    func createProduct(_ request: CreateProductRequest) async throws -> Product {
        print("📦 Creating product: \(request.nameHebrew)")

        let response: Product = try await safeClient.database
            .from("products")
            .insert(request)
            .select()
            .single()
            .execute()
            .value

        print("✅ Product created with ID: \(response.id)")
        return response
    }

    // MARK: - Document Update Operations

    func updateDocument(_ request: UpdateDocumentRequest) async throws -> Document {
        print("📝 Updating document: \(request.documentId)")
        let startTime = Date()

        // Debug: Print the request being sent
        do {
            let requestData = try JSONEncoder().encode(request)
            if let requestString = String(data: requestData, encoding: .utf8) {
                print("🔍 Update request: \(requestString)")
            }
        } catch {
            print("❌ Failed to encode request: \(error)")
        }

        let response: Data
        do {
            print("🚀 Starting function invoke at: \(startTime)")

            // Try using the invoke method with explicit return type
            let functionResponse: DocumentResponse = try await safeClient.functions.invoke(
                "documents-update",
                options: FunctionInvokeOptions(
                    body: try JSONEncoder().encode(request)
                )
            )

            // Convert the response back to Data for consistency with existing code
            response = try JSONEncoder().encode(functionResponse)

            let endTime = Date()
            let duration = endTime.timeIntervalSince(startTime)
            print("✅ Function invoke completed in: \(duration)s")
            print("📊 Response size: \(response.count) bytes")
        } catch {
            let endTime = Date()
            let duration = endTime.timeIntervalSince(startTime)
            print("❌ Function invoke FAILED after: \(duration)s")
            print("❌ Function invoke error: \(error)")
            print("❌ Function invoke error details: \(error.localizedDescription)")
            print("❌ Error type: \(type(of: error))")

            // Print the full error object
            print("❌ Full error object: \(error)")

            // Check if it's a network/HTTP error
            if error.localizedDescription.contains("couldn't be read") ||
               error.localizedDescription.contains("correct format") {
                print("❌ This appears to be a network/HTTP level error, not a JSON parsing error")
                print("❌ Possible causes: network timeout, server error, or malformed HTTP response")
            }

            throw SupabaseError.networkError
        }

        // Debug: Print raw response
        print("📥 Received response with \(response.count) bytes")
        if let responseString = String(data: response, encoding: .utf8) {
            print("🔍 Raw update response: \(responseString)")
        } else {
            print("❌ Could not convert response to UTF-8 string")
        }

        // Try to parse as generic JSON first to see the structure
        do {
            if let jsonObject = try JSONSerialization.jsonObject(with: response) as? [String: Any] {
                print("🔍 Parsed JSON structure:")
                print("  - success: \(jsonObject["success"] ?? "nil")")
                print("  - error: \(jsonObject["error"] ?? "nil")")
                if let data = jsonObject["data"] as? [String: Any] {
                    print("  - data keys: \(data.keys)")
                    if let document = data["document"] as? [String: Any] {
                        print("  - document keys: \(document.keys)")
                        // Check for problematic fields
                        for (key, value) in document {
                            let valueType = type(of: value)
                            print("    - \(key): \(valueType)")
                            if key.contains("date") || key.contains("number") || key.contains("id") {
                                print("      value: \(value)")
                            }
                        }
                    }
                    if let itaSubmission = data["ita_submission"] as? [String: Any] {
                        print("  - ita_submission keys: \(itaSubmission.keys)")
                        for (key, value) in itaSubmission {
                            print("    - \(key): \(type(of: value)) = \(value)")
                        }
                    }
                }
            }
        } catch {
            print("❌ Failed to parse as generic JSON: \(error)")
        }

        do {
            print("🔄 Starting JSON decoding...")

            // First, let's try to see what type of object we're dealing with
            if let jsonObject = try? JSONSerialization.jsonObject(with: response, options: []) {
                print("🔍 Response is valid JSON object of type: \(type(of: jsonObject))")
                if let dict = jsonObject as? [String: Any] {
                    print("🔍 JSON keys: \(dict.keys.sorted())")
                    if let success = dict["success"] {
                        print("🔍 Success field type: \(type(of: success)), value: \(success)")
                    }
                    if let data = dict["data"] {
                        print("🔍 Data field type: \(type(of: data))")
                    }
                    if let error = dict["error"] {
                        print("🔍 Error field type: \(type(of: error)), value: \(error)")
                    }
                }
            } else {
                print("❌ Response is not valid JSON")
            }

            let result = try JSONDecoder().decode(DocumentResponse.self, from: response)
            print("✅ Successfully decoded DocumentResponse")
            print("🔍 DocumentResponse success: \(result.success)")
            if let data = result.data {
                print("🔍 Document ID: \(data.document.id)")
                print("🔍 Document status: \(data.document.status)")
                if let itaSubmission = data.itaSubmission {
                    print("🔍 ITA submission status: \(itaSubmission.status)")
                }
            }
            print("🔄 Calling handleDocumentResponse...")
            return try await handleDocumentResponse(result)
        } catch {
            print("❌ JSON DECODING FAILED")
            print("❌ Failed to decode DocumentResponse: \(error)")
            print("❌ Decoding error details: \(error.localizedDescription)")
            print("❌ Decoding error type: \(type(of: error))")

            // Print more detailed error information
            if let decodingError = error as? DecodingError {
                switch decodingError {
                case .keyNotFound(let key, let context):
                    print("❌ Key '\(key)' not found: \(context.debugDescription)")
                    print("❌ Coding path: \(context.codingPath.map { $0.stringValue }.joined(separator: " -> "))")
                case .valueNotFound(let value, let context):
                    print("❌ Value '\(value)' not found: \(context.debugDescription)")
                    print("❌ Coding path: \(context.codingPath.map { $0.stringValue }.joined(separator: " -> "))")
                case .typeMismatch(let type, let context):
                    print("❌ Type '\(type)' mismatch: \(context.debugDescription)")
                    print("❌ Coding path: \(context.codingPath.map { $0.stringValue }.joined(separator: " -> "))")
                case .dataCorrupted(let context):
                    print("❌ Data corrupted: \(context.debugDescription)")
                    print("❌ Coding path: \(context.codingPath.map { $0.stringValue }.joined(separator: " -> "))")
                @unknown default:
                    print("❌ Unknown decoding error: \(decodingError)")
                }
            }

            // Try to decode as a generic error response
            do {
                if let errorDict = try JSONSerialization.jsonObject(with: response) as? [String: Any] {
                    print("❌ Server response: \(errorDict)")
                    if let errorMessage = errorDict["error"] as? String {
                        throw SupabaseError.serverError(errorMessage)
                    }
                }
            } catch {
                print("❌ Could not parse error response")
            }

            throw error
        }
    }

    private func handleDocumentResponse(_ result: DocumentResponse) async throws -> Document {

        if result.success, let data = result.data {
            print("✅ Document updated successfully: \(data.document.id)")
            return data.document
        } else {
            print("❌ Failed to update document: \(result.error ?? "Unknown error")")
            throw SupabaseError.unknownError
        }
    }

    func getDocumentItems(documentId: String) async throws -> [DocumentItem] {
        print("📋 Loading items for document: \(documentId)")

        let response: [DocumentItem] = try await safeClient.database
            .from("document_items")
            .select("*")
            .eq("document_id", value: documentId)
            .order("line_number")
            .execute()
            .value

        print("✅ Loaded \(response.count) document items")
        return response
    }

    func getCustomerById(_ customerId: String) async throws -> Customer {
        print("👤 Loading customer: \(customerId)")

        let response: Customer = try await safeClient.database
            .from("customers")
            .select("*")
            .eq("id", value: customerId)
            .single()
            .execute()
            .value

        print("✅ Loaded customer: \(response.nameHebrew)")
        return response
    }
    
    // MARK: - Configuration Helpers
    
    private func getSupabaseURL() -> URL? {
        // Try to get from environment variables or configuration
        if let urlString = ProcessInfo.processInfo.environment["SUPABASE_URL"] {
            return URL(string: urlString)
        }
        
        // Fallback to configuration file or hardcoded value
        // In production, this should come from a secure configuration
        if let path = Bundle.main.path(forResource: "Config", ofType: "plist"),
           let config = NSDictionary(contentsOfFile: path),
           let urlString = config["SUPABASE_URL"] as? String {
            return URL(string: urlString)
        }
        
        return nil
    }
    
    private func getSupabaseAnonKey() -> String? {
        // Try to get from environment variables or configuration
        if let key = ProcessInfo.processInfo.environment["SUPABASE_ANON_KEY"] {
            return key
        }
        
        // Fallback to configuration file
        if let path = Bundle.main.path(forResource: "Config", ofType: "plist"),
           let config = NSDictionary(contentsOfFile: path),
           let key = config["SUPABASE_ANON_KEY"] as? String {
            return key
        }
        
        return nil
    }
}

// MARK: - Error Handling
extension SupabaseService {
    enum SupabaseError: LocalizedError {
        case configurationMissing
        case authenticationFailed
        case networkError
        case unknownError
        case serverError(String)
        case customerAlreadyExists(String)

        var errorDescription: String? {
            switch self {
            case .configurationMissing:
                return "Supabase configuration is missing"
            case .authenticationFailed:
                return "Authentication failed"
            case .networkError:
                return "Network error occurred"
            case .unknownError:
                return "An unknown error occurred"
            case .serverError(let message):
                return "Server error: \(message)"
            case .customerAlreadyExists(let businessNumber):
                return "לקוח עם מספר עסק \(businessNumber) כבר קיים במערכת"
            }
        }
    }
}
