import SwiftUI

struct CustomerManagementView: View {
    @ObservedObject var documentViewModel: DocumentViewModel
    @Environment(\.dismiss) private var dismiss
    @State private var showingDeleteConfirmation = false
    @State private var customerToDelete: Customer?
    
    var body: some View {
        NavigationView {
            VStack(spacing: .spacing4) {
                // Search bar
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.mutedForeground)
                    
                    TextField("חפש לקוח...", text: $documentViewModel.customerSearchQuery)
                        .textFieldStyle(PlainTextFieldStyle())
                        .multilineTextAlignment(.trailing)
                }
                .padding(.spacing3)
                .background(Color.muted.opacity(0.3))
                .clipShape(RoundedRectangle(cornerRadius: .radiusSmall))
                
                // Create New Customer Button
                Button("צור לקוח חדש") {
                    documentViewModel.customerFormData.reset()
                    documentViewModel.showingCreateCustomer = true
                }
                .buttonStyle(CosmicSecondaryButtonStyle())
                
                // Customer List
                if documentViewModel.isLoading {
                    VStack {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle())
                        Text("טוען לקוחות...")
                            .font(.hebrewCaption)
                            .foregroundColor(.mutedForeground)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else if documentViewModel.filteredCustomers.isEmpty {
                    VStack {
                        Image(systemName: "person.3")
                            .font(.largeTitle)
                            .foregroundColor(.mutedForeground)
                        Text("לא נמצאו לקוחות")
                            .font(.hebrewBody)
                            .foregroundColor(.mutedForeground)
                        Text("צור לקוח חדש כדי להתחיל")
                            .font(.hebrewCaption)
                            .foregroundColor(.mutedForeground)
                    }
                    .frame(maxWidth: .infinity, minHeight: 200)
                } else {
                    ScrollView {
                        LazyVStack(spacing: .spacing3) {
                            ForEach(documentViewModel.filteredCustomers) { customer in
                                CustomerManagementRowView(
                                    customer: customer,
                                    onEdit: {
                                        documentViewModel.editCustomer(customer)
                                    },
                                    onDelete: {
                                        customerToDelete = customer
                                        showingDeleteConfirmation = true
                                    }
                                )
                            }
                        }
                        .padding(.horizontal, .spacing1)
                    }
                }
                
                Spacer()
            }
            .padding(.spacing4)
            .background(Color.background)
            .navigationTitle("ניהול לקוחות")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("סגור") {
                        dismiss()
                    }
                    .foregroundColor(.primary)
                }
            }
        }
        .environment(\.layoutDirection, .rightToLeft)
        .sheet(isPresented: $documentViewModel.showingCreateCustomer) {
            CreateCustomerView(documentViewModel: documentViewModel)
        }
        .sheet(isPresented: $documentViewModel.showingEditCustomer) {
            EditCustomerView(documentViewModel: documentViewModel)
        }
        .alert("מחיקת לקוח", isPresented: $showingDeleteConfirmation) {
            Button("ביטול", role: .cancel) {
                customerToDelete = nil
            }
            Button("מחק", role: .destructive) {
                if let customer = customerToDelete {
                    documentViewModel.deleteCustomer(customer)
                }
                customerToDelete = nil
            }
        } message: {
            if let customer = customerToDelete {
                Text("האם אתה בטוח שברצונך למחוק את הלקוח '\(customer.displayName)'? פעולה זו לא ניתנת לביטול.")
            }
        }
        .onChange(of: documentViewModel.successMessage) { successMessage in
            if successMessage != nil {
                // Auto-clear success message after 3 seconds
                DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                    documentViewModel.successMessage = nil
                }
            }
        }
        .overlay(
            // Success message overlay
            Group {
                if let successMessage = documentViewModel.successMessage {
                    VStack {
                        Spacer()
                        HStack {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                            Text(successMessage)
                                .font(.hebrewBody)
                                .foregroundColor(.cardForeground)
                        }
                        .padding(.spacing3)
                        .background(Color.card)
                        .clipShape(RoundedRectangle(cornerRadius: .radiusSmall))
                        .shadow(radius: 4)
                        .padding(.spacing4)
                    }
                    .transition(.move(edge: .bottom).combined(with: .opacity))
                    .animation(.easeInOut(duration: 0.3), value: documentViewModel.successMessage)
                }
            }
        )
        .onAppear {
            documentViewModel.loadCustomers()
        }
    }
}

// MARK: - Customer Management Row View
struct CustomerManagementRowView: View {
    let customer: Customer
    let onEdit: () -> Void
    let onDelete: () -> Void
    
    var body: some View {
        HStack(spacing: .spacing3) {
            // Customer Info
            VStack(alignment: .trailing, spacing: .spacing1) {
                Text(customer.displayName)
                    .font(.hebrewBody.weight(.medium))
                    .foregroundColor(.cardForeground)
                    .multilineTextAlignment(.trailing)
                
                Text(customer.fullAddress)
                    .font(.caption2)
                    .foregroundColor(.mutedForeground)
                    .multilineTextAlignment(.trailing)
                    .lineLimit(2)
                
                HStack(spacing: .spacing2) {
                    if let vatId = customer.formattedVatId {
                        Text("ע.מ: \(vatId)")
                            .font(.caption2)
                            .foregroundColor(.mutedForeground)
                    }
                    
                    Text("עסק: \(customer.formattedBusinessNumber)")
                        .font(.caption2)
                        .foregroundColor(.mutedForeground)
                }
            }
            
            Spacer()
            
            // Contact Info Icons
            VStack(alignment: .leading, spacing: .spacing1) {
                if customer.hasContactInfo {
                    HStack(spacing: .spacing2) {
                        if customer.contactEmail != nil {
                            Image(systemName: "envelope")
                                .font(.caption2)
                                .foregroundColor(.mutedForeground)
                        }
                        if customer.contactPhone != nil {
                            Image(systemName: "phone")
                                .font(.caption2)
                                .foregroundColor(.mutedForeground)
                        }
                    }
                }
                
                // Action buttons
                HStack(spacing: .spacing2) {
                    // Edit button
                    Button(action: onEdit) {
                        Image(systemName: "pencil")
                            .font(.caption)
                            .foregroundColor(.primary)
                            .frame(width: 32, height: 32)
                            .background(Color.primary.opacity(0.1))
                            .clipShape(Circle())
                    }
                    .buttonStyle(PlainButtonStyle())
                    
                    // Delete button
                    Button(action: onDelete) {
                        Image(systemName: "trash")
                            .font(.caption)
                            .foregroundColor(.red)
                            .frame(width: 32, height: 32)
                            .background(Color.red.opacity(0.1))
                            .clipShape(Circle())
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
        .padding(.spacing3)
        .background(
            RoundedRectangle(cornerRadius: .radiusSmall)
                .fill(Color.muted.opacity(0.3))
        )
    }
}

#Preview {
    CustomerManagementView(documentViewModel: DocumentViewModel())
}
