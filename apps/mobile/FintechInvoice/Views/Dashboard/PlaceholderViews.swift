import SwiftUI

// MARK: - Documents View
struct DocumentsView: View {
    @StateObject private var documentsViewModel = DocumentsListViewModel()
    @EnvironmentObject var authViewModel: AuthViewModel
    @State private var showingDocumentCreation = false
    @State private var selectedStatusFilter: DocumentStatus?

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Search and Filter Bar
                VStack(spacing: .spacing3) {
                    // Search Bar
                    HStack {
                        Image(systemName: "magnifyingglass")
                            .foregroundColor(.mutedForeground)

                        TextField("חפש מסמכים...", text: $documentsViewModel.searchQuery)
                            .textFieldStyle(PlainTextFieldStyle())
                            .font(.hebrewBody)
                    }
                    .padding(.spacing3)
                    .background(Color.muted.opacity(0.3))
                    .clipShape(RoundedRectangle(cornerRadius: .radiusSmall))

                    // Status Filter
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: .spacing2) {
                            StatusFilterChip(
                                title: "הכל",
                                isSelected: documentsViewModel.selectedStatusFilter == nil
                            ) {
                                documentsViewModel.selectedStatusFilter = nil
                            }

                            ForEach(DocumentStatus.allCases, id: \.self) { status in
                                StatusFilterChip(
                                    title: status.displayName,
                                    isSelected: documentsViewModel.selectedStatusFilter == status
                                ) {
                                    documentsViewModel.selectedStatusFilter = status
                                }
                            }
                        }
                        .padding(.horizontal, .spacing4)
                    }
                }
                .padding(.horizontal, .spacing4)
                .padding(.top, .spacing2)

                // Content
                if documentsViewModel.isLoading {
                    VStack {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle())
                        Text("טוען מסמכים...")
                            .font(.hebrewCaption)
                            .foregroundColor(.mutedForeground)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else if documentsViewModel.filteredDocuments.isEmpty {
                    EmptyDocumentsView(showingDocumentCreation: $showingDocumentCreation)
                } else {
                    DocumentsListView(documentsViewModel: documentsViewModel)
                }

                // Error Message
                if let errorMessage = documentsViewModel.errorMessage {
                    Text(errorMessage)
                        .font(.hebrewCaption)
                        .foregroundColor(.destructive)
                        .padding(.horizontal, .spacing4)
                        .padding(.bottom, .spacing2)
                }
            }
            .background(Color.background)
            .navigationTitle("מסמכים")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("חדש") {
                        showingDocumentCreation = true
                    }
                    .foregroundColor(.primary)
                }

                ToolbarItem(placement: .navigationBarLeading) {
                    Button("רענן") {
                        documentsViewModel.refreshDocuments()
                    }
                    .foregroundColor(.primary)
                }
            }
        }
        .environment(\.layoutDirection, .rightToLeft)
        .sheet(isPresented: $showingDocumentCreation) {
            DocumentCreationWizardView()
        }
        .onAppear {
            // Set the company ID and load documents
            if let selectedCompany = authViewModel.selectedCompany {
                documentsViewModel.setCompanyId(selectedCompany.id)
                documentsViewModel.loadDocuments()
            }
        }
    }
}

// MARK: - Supporting Views for Documents

struct StatusFilterChip: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.hebrewCaption)
                .foregroundColor(isSelected ? .white : .cardForeground)
                .padding(.horizontal, .spacing3)
                .padding(.vertical, .spacing2)
                .background(isSelected ? Color.primary : Color.muted.opacity(0.3))
                .clipShape(RoundedRectangle(cornerRadius: .radiusSmall))
        }
    }
}

struct EmptyDocumentsView: View {
    @Binding var showingDocumentCreation: Bool

    var body: some View {
        VStack(spacing: .spacing6) {
            Image(systemName: "doc.text.fill")
                .font(.system(size: 60))
                .foregroundColor(.mutedForeground)

            Text("אין מסמכים")
                .font(.hebrewHeading.weight(.semibold))
                .foregroundColor(.cardForeground)

            Text("צור את החשבונית הראשונה שלך כדי להתחיל")
                .font(.hebrewBody)
                .foregroundColor(.mutedForeground)
                .multilineTextAlignment(.center)

            Button("צור חשבונית חדשה") {
                showingDocumentCreation = true
            }
            .buttonStyle(CosmicPrimaryButtonStyle())
        }
        .padding(.spacing6)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

struct DocumentsListView: View {
    @ObservedObject var documentsViewModel: DocumentsListViewModel

    var body: some View {
        List(documentsViewModel.filteredDocuments) { document in
            DocumentListRowView(document: document, documentsViewModel: documentsViewModel)
        }
        .listStyle(PlainListStyle())
        .refreshable {
            documentsViewModel.refreshDocuments()
        }
    }
}

struct DocumentListRowView: View {
    let document: Document
    @ObservedObject var documentsViewModel: DocumentsListViewModel
    @State private var showingDocumentEdit = false

    var body: some View {
        Button(action: {
            handleDocumentTap()
        }) {
            VStack(alignment: .trailing, spacing: .spacing2) {
                HStack {
                    VStack(alignment: .leading, spacing: .spacing1) {
                        // Status and Date
                        HStack {
                            Text(document.status.displayName)
                                .font(.caption2)
                                .foregroundColor(statusColor)
                                .padding(.horizontal, .spacing2)
                                .padding(.vertical, 2)
                                .background(statusColor.opacity(0.1))
                                .clipShape(RoundedRectangle(cornerRadius: 4))

                            Spacer()
                        }

                        Text(documentsViewModel.formatDate(document.issueDate))
                            .font(.caption2)
                            .foregroundColor(.mutedForeground)
                    }

                    Spacer()

                    VStack(alignment: .trailing, spacing: .spacing1) {
                        // Customer Name (moved from bottom)
                        Text(documentsViewModel.getCustomerName(for: document))
                            .font(.hebrewBody.weight(.medium))
                            .foregroundColor(.cardForeground)
                            .multilineTextAlignment(.trailing)

                        Text(document.documentType.displayName)
                            .font(.caption2)
                            .foregroundColor(.mutedForeground)

                        // Amount
                        Text(documentsViewModel.formatAmount(document.totalAmount))
                            .font(.hebrewCaption.weight(.semibold))
                            .foregroundColor(.cardForeground)
                    }
                }

                // Document ID (moved from top right)
                HStack {
                    Spacer()
                    Text("מזהה: \(document.id.prefix(8))")
                        .font(.caption2)
                        .foregroundColor(.mutedForeground)
                }
            }
            .padding(.spacing3)
            .background(Color.card)
            .clipShape(RoundedRectangle(cornerRadius: .radiusSmall))
        }
        .buttonStyle(PlainButtonStyle())
        .listRowSeparator(.hidden)
        .listRowBackground(Color.clear)
        .sheet(isPresented: $showingDocumentEdit) {
            if document.status == .draft {
                DocumentEditView(document: document)
            }
        }
    }

    private func handleDocumentTap() {
        if document.status == .draft {
            // Open document for editing
            showingDocumentEdit = true
        } else {
            // Open PDF for non-draft documents
            openDocumentPDF()
        }
    }

    private func openDocumentPDF() {
        // Check if PDF URL exists
        guard let pdfUrl = document.pdfUrl,
              let url = URL(string: pdfUrl) else {
            // TODO: Show error message that PDF is not available
            print("❌ PDF URL not available for document: \(document.id)")
            return
        }

        // Open PDF URL in Safari or default browser
        UIApplication.shared.open(url)
    }

    private var statusColor: Color {
        switch document.status {
        case .draft:
            return .gray
        case .pendingAllocation:
            return .orange
        case .approved:
            return .blue
        case .sent:
            return .purple
        case .paid:
            return .green
        case .cancelled:
            return .red
        }
    }
}

// MARK: - Customers View
struct CustomersView: View {
    @StateObject private var documentViewModel = DocumentViewModel()
    @EnvironmentObject var authViewModel: AuthViewModel
    @State private var showingDeleteConfirmation = false
    @State private var customerToDelete: Customer?

    var body: some View {
        NavigationView {
            VStack(spacing: .spacing4) {
                // Search Bar
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.mutedForeground)

                    TextField("חפש לקוחות...", text: $documentViewModel.customerSearchQuery)
                        .textFieldStyle(CosmicTextFieldStyle())
                        .multilineTextAlignment(.trailing)
                }
                .padding(.horizontal, .spacing4)

                // Create New Customer Button
                Button("צור לקוח חדש") {
                    documentViewModel.showingCreateCustomer = true
                }
                .buttonStyle(CosmicPrimaryButtonStyle())
                .padding(.horizontal, .spacing4)

                // Customer List
                if documentViewModel.isLoading {
                    VStack {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle())
                        Text("טוען לקוחות...")
                            .font(.hebrewCaption)
                            .foregroundColor(.mutedForeground)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else if documentViewModel.filteredCustomers.isEmpty {
                    VStack(spacing: .spacing4) {
                        Image(systemName: "person.2")
                            .font(.system(size: 40))
                            .foregroundColor(.mutedForeground)

                        Text("אין לקוחות")
                            .font(.hebrewBody.weight(.medium))
                            .foregroundColor(.cardForeground)

                        Text("צור לקוח ראשון כדי להתחיל")
                            .font(.hebrewCaption)
                            .foregroundColor(.mutedForeground)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else {
                    List(documentViewModel.filteredCustomers) { customer in
                        CustomerRowViewForList(
                            customer: customer,
                            onEdit: {
                                documentViewModel.editCustomer(customer)
                            },
                            onDelete: {
                                customerToDelete = customer
                                showingDeleteConfirmation = true
                            }
                        )
                    }
                    .listStyle(PlainListStyle())
                    .refreshable {
                        documentViewModel.loadCustomers()
                    }
                }

                // Success Message
                if let successMessage = documentViewModel.successMessage {
                    HStack {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.success)

                        Text(successMessage)
                            .font(.hebrewCaption)
                            .foregroundColor(.success)
                    }
                    .padding(.horizontal, .spacing4)
                    .padding(.vertical, .spacing2)
                    .background(Color.success.opacity(0.1))
                    .clipShape(RoundedRectangle(cornerRadius: .radiusSmall))
                    .padding(.horizontal, .spacing4)
                }

                // Error Message
                if let errorMessage = documentViewModel.errorMessage {
                    HStack {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .foregroundColor(.destructive)

                        Text(errorMessage)
                            .font(.hebrewCaption)
                            .foregroundColor(.destructive)
                    }
                    .padding(.horizontal, .spacing4)
                    .padding(.vertical, .spacing2)
                    .background(Color.destructive.opacity(0.1))
                    .clipShape(RoundedRectangle(cornerRadius: .radiusSmall))
                    .padding(.horizontal, .spacing4)
                }
            }
            .background(Color.background)
            .navigationTitle("לקוחות")
            .navigationBarTitleDisplayMode(.large)
            .onAppear {
                // Set the company ID and load customers
                if let selectedCompany = authViewModel.selectedCompany {
                    documentViewModel.setCompanyId(selectedCompany.id)
                    documentViewModel.loadCustomers()
                }

                // Clear any previous messages
                documentViewModel.errorMessage = nil
                documentViewModel.successMessage = nil
            }
            .onChange(of: documentViewModel.successMessage) { successMessage in
                if successMessage != nil {
                    // Auto-clear success message after 3 seconds
                    DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                        documentViewModel.successMessage = nil
                    }
                }
            }
            .sheet(isPresented: $documentViewModel.showingCreateCustomer) {
                CreateCustomerView(documentViewModel: documentViewModel)
            }
            .sheet(isPresented: $documentViewModel.showingEditCustomer) {
                EditCustomerView(documentViewModel: documentViewModel)
            }
            .alert("מחיקת לקוח", isPresented: $showingDeleteConfirmation) {
                Button("ביטול", role: .cancel) {
                    customerToDelete = nil
                }
                Button("מחק", role: .destructive) {
                    if let customer = customerToDelete {
                        documentViewModel.deleteCustomer(customer)
                    }
                    customerToDelete = nil
                }
            } message: {
                if let customer = customerToDelete {
                    Text("האם אתה בטוח שברצונך למחוק את הלקוח '\(customer.displayName)'? פעולה זו לא ניתנת לביטול.")
                }
            }
        }
        .environment(\.layoutDirection, .rightToLeft)
    }
}

// MARK: - Customer Row View for List
struct CustomerRowViewForList: View {
    let customer: Customer
    let onEdit: () -> Void
    let onDelete: () -> Void

    var body: some View {
        VStack(alignment: .trailing, spacing: .spacing2) {
            HStack {
                // Action buttons
                VStack(spacing: .spacing2) {
                    // Edit button
                    Button(action: onEdit) {
                        Image(systemName: "pencil")
                            .font(.caption)
                            .foregroundColor(.primary)
                            .frame(width: 32, height: 32)
                            .background(Color.primary.opacity(0.1))
                            .clipShape(Circle())
                    }
                    .buttonStyle(PlainButtonStyle())

                    // Delete button
                    Button(action: onDelete) {
                        Image(systemName: "trash")
                            .font(.caption)
                            .foregroundColor(.red)
                            .frame(width: 32, height: 32)
                            .background(Color.red.opacity(0.1))
                            .clipShape(Circle())
                    }
                    .buttonStyle(PlainButtonStyle())
                }

                Spacer()

                VStack(alignment: .trailing, spacing: .spacing1) {
                    Text(customer.displayName)
                        .font(.hebrewBody.weight(.medium))
                        .foregroundColor(.cardForeground)
                        .multilineTextAlignment(.trailing)

                    Text(customer.fullAddress)
                        .font(.hebrewCaption)
                        .foregroundColor(.mutedForeground)
                        .multilineTextAlignment(.trailing)
                        .lineLimit(2)

                    HStack(spacing: .spacing2) {
                        if let vatId = customer.formattedVatId {
                            Text("ע.מ: \(vatId)")
                                .font(.caption2)
                                .foregroundColor(.mutedForeground)
                        }

                        Text("עסק: \(customer.formattedBusinessNumber)")
                            .font(.caption2)
                            .foregroundColor(.mutedForeground)
                    }

                    // Contact info
                    if customer.hasContactInfo {
                        HStack(spacing: .spacing2) {
                            if customer.contactEmail != nil {
                                Image(systemName: "envelope")
                                    .font(.caption2)
                                    .foregroundColor(.mutedForeground)
                            }
                            if customer.contactPhone != nil {
                                Image(systemName: "phone")
                                    .font(.caption2)
                                    .foregroundColor(.mutedForeground)
                            }
                        }
                    }
                }
            }
        }
        .padding(.spacing3)
        .background(Color.card)
        .clipShape(RoundedRectangle(cornerRadius: .radiusSmall))
        .listRowSeparator(.hidden)
        .listRowBackground(Color.clear)
    }
}

// MARK: - Expenses View
struct ExpensesView: View {
    var body: some View {
        NavigationView {
            VStack(spacing: .spacing6) {
                Image(systemName: "creditcard.fill")
                    .font(.system(size: 60))
                    .foregroundColor(.mutedForeground)
                
                Text("הוצאות")
                    .font(.hebrewHeading.weight(.semibold))
                    .foregroundColor(.cardForeground)
                
                Text("סרוק ונהל את ההוצאות העסקיות שלך")
                    .font(.hebrewBody)
                    .foregroundColor(.mutedForeground)
                    .multilineTextAlignment(.center)
                
                VStack(spacing: .spacing4) {
                    Button("סרוק קבלה") {
                        // TODO: Open camera for receipt scanning
                    }
                    .buttonStyle(CosmicPrimaryButtonStyle())
                    
                    Button("הוסף הוצאה ידנית") {
                        // TODO: Navigate to manual expense entry
                    }
                    .buttonStyle(CosmicSecondaryButtonStyle())
                }
            }
            .padding(.spacing6)
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(Color.background)
            .navigationTitle("הוצאות")
            .navigationBarTitleDisplayMode(.large)
        }
        .environment(\.layoutDirection, .rightToLeft)
    }
}

// MARK: - Settings View
struct SettingsView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @State private var showingLogoutAlert = false
    
    var body: some View {
        NavigationView {
            List {
                // User Info Section
                Section {
                    if let user = authViewModel.currentUser {
                        HStack {
                            Image(systemName: "person.circle.fill")
                                .font(.title)
                                .foregroundColor(.primary)
                            
                            VStack(alignment: .leading) {
                                Text(user.email ?? "")
                                    .font(.hebrewBody)
                                    .foregroundColor(.cardForeground)
                                
                                if let company = authViewModel.selectedCompany {
                                    Text(company.name)
                                        .font(.hebrewCaption)
                                        .foregroundColor(.mutedForeground)
                                }
                            }
                            
                            Spacer()
                        }
                        .padding(.vertical, .spacing2)
                    }
                }
                
                // App Settings Section
                Section("הגדרות אפליקציה") {
                    SettingsRow(
                        icon: "bell",
                        title: "התראות",
                        subtitle: "נהל התראות האפליקציה"
                    ) {
                        // TODO: Navigate to notifications settings
                    }
                    
                    SettingsRow(
                        icon: "moon",
                        title: "מצב כהה",
                        subtitle: "תמיד מופעל"
                    ) {
                        // TODO: Theme settings
                    }
                    
                    SettingsRow(
                        icon: "globe",
                        title: "שפה",
                        subtitle: "עברית"
                    ) {
                        // TODO: Language settings
                    }
                }
                
                // Company Settings Section
                Section("הגדרות חברה") {
                    SettingsRow(
                        icon: "building.2",
                        title: "פרטי החברה",
                        subtitle: "עדכן פרטי החברה"
                    ) {
                        // TODO: Navigate to company settings
                    }
                    
                    SettingsRow(
                        icon: "person.2",
                        title: "משתמשים",
                        subtitle: "נהל משתמשי החברה"
                    ) {
                        // TODO: Navigate to users management
                    }
                    
                    SettingsRow(
                        icon: "creditcard",
                        title: "מנוי",
                        subtitle: authViewModel.selectedCompany?.subscriptionTier == "free" ? "חינמי" : "מתקדם"
                    ) {
                        // TODO: Navigate to subscription
                    }
                }
                
                // Support Section
                Section("תמיכה") {
                    SettingsRow(
                        icon: "questionmark.circle",
                        title: "עזרה ותמיכה",
                        subtitle: "מרכז העזרה"
                    ) {
                        // TODO: Open help center
                    }
                    
                    SettingsRow(
                        icon: "envelope",
                        title: "צור קשר",
                        subtitle: "שלח הודעה לתמיכה"
                    ) {
                        // TODO: Open contact form
                    }
                    
                    SettingsRow(
                        icon: "star",
                        title: "דרג את האפליקציה",
                        subtitle: "App Store"
                    ) {
                        // TODO: Open App Store rating
                    }
                }
                
                // Account Section
                Section("חשבון") {
                    Button(action: {
                        showingLogoutAlert = true
                    }) {
                        HStack {
                            Image(systemName: "rectangle.portrait.and.arrow.right")
                                .foregroundColor(.destructive)
                            
                            Text("התנתק")
                                .font(.hebrewBody)
                                .foregroundColor(.destructive)
                            
                            Spacer()
                        }
                    }
                }
            }
            .listStyle(InsetGroupedListStyle())
            .background(Color.background)
            .navigationTitle("הגדרות")
            .navigationBarTitleDisplayMode(.large)
            .alert("התנתקות", isPresented: $showingLogoutAlert) {
                Button("בטל", role: .cancel) { }
                Button("התנתק", role: .destructive) {
                    Task {
                        await authViewModel.signOut()
                    }
                }
            } message: {
                Text("האם אתה בטוח שברצונך להתנתק?")
            }
        }
        .environment(\.layoutDirection, .rightToLeft)
    }
}

struct SettingsRow: View {
    let icon: String
    let title: String
    let subtitle: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(.primary)
                    .frame(width: 24)
                
                VStack(alignment: .leading, spacing: .spacing1) {
                    Text(title)
                        .font(.hebrewBody)
                        .foregroundColor(.cardForeground)
                    
                    Text(subtitle)
                        .font(.hebrewCaption)
                        .foregroundColor(.mutedForeground)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .foregroundColor(.mutedForeground)
                    .font(.caption)
            }
            .padding(.vertical, .spacing1)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview("Documents") {
    DocumentsView()
}

#Preview("Customers") {
    CustomersView()
}

// MARK: - Edit Customer View
struct EditCustomerView: View {
    @ObservedObject var documentViewModel: DocumentViewModel
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: .spacing6) {
                    headerSection
                    formSection
                    buttonSection
                }
                .padding(.spacing4)
            }
            .background(Color.background)
            .navigationTitle("עריכת לקוח")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("ביטול") {
                        dismiss()
                    }
                    .foregroundColor(.primary)
                }
            }
        }
        .environment(\.layoutDirection, .rightToLeft)
        .onChange(of: documentViewModel.successMessage) { successMessage in
            if successMessage != nil {
                // Auto-dismiss after showing success message for 1.5 seconds
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                    dismiss()
                }
            }
        }
    }

    private var headerSection: some View {
        VStack(alignment: .trailing, spacing: .spacing3) {
            Text("עריכת פרטי לקוח")
                .font(.hebrewHeading.weight(.semibold))
                .foregroundColor(.cardForeground)

            Text("עדכן את פרטי הלקוח")
                .font(.hebrewBody)
                .foregroundColor(.mutedForeground)
                .multilineTextAlignment(.trailing)
        }
    }

    private var formSection: some View {
        LazyVStack(spacing: .spacing4) {
            companyNameField
            businessNumberField
            addressField
            cityField
            emailField
            phoneField
            notesField
        }
    }

    private var companyNameField: some View {
        VStack(alignment: .trailing, spacing: .spacing2) {
            Text("שם החברה *")
                .font(.hebrewBody.weight(.medium))
                .foregroundColor(.cardForeground)

            TextField("שם החברה", text: $documentViewModel.customerFormData.nameHebrew)
                .textFieldStyle(CosmicTextFieldStyle())
                .multilineTextAlignment(.trailing)
        }
    }

    private var businessNumberField: some View {
        VStack(alignment: .trailing, spacing: .spacing2) {
            Text("ע.מ / ח.פ *")
                .font(.hebrewBody.weight(.medium))
                .foregroundColor(.cardForeground)

            TextField("*********", text: $documentViewModel.customerFormData.businessNumber)
                .textFieldStyle(CosmicTextFieldStyle())
                .keyboardType(.numberPad)
                .multilineTextAlignment(.trailing)

            if let error = documentViewModel.customerFormData.businessNumberError {
                Text(error)
                    .font(.hebrewCaption)
                    .foregroundColor(.destructive)
                    .multilineTextAlignment(.trailing)
            }
        }
    }

    private var addressField: some View {
        VStack(alignment: .trailing, spacing: .spacing2) {
            Text("כתובת *")
                .font(.hebrewBody.weight(.medium))
                .foregroundColor(.cardForeground)

            TextField("רחוב 123", text: $documentViewModel.customerFormData.billingAddressHebrew)
                .textFieldStyle(CosmicTextFieldStyle())
                .multilineTextAlignment(.trailing)
        }
    }

    private var cityField: some View {
        VStack(alignment: .trailing, spacing: .spacing2) {
            Text("עיר *")
                .font(.hebrewBody.weight(.medium))
                .foregroundColor(.cardForeground)

            TextField("תל אביב", text: $documentViewModel.customerFormData.cityHebrew)
                .textFieldStyle(CosmicTextFieldStyle())
                .multilineTextAlignment(.trailing)
        }
    }

    private var emailField: some View {
        VStack(alignment: .trailing, spacing: .spacing2) {
            Text("אימייל")
                .font(.hebrewBody.weight(.medium))
                .foregroundColor(.cardForeground)

            TextField("<EMAIL>", text: $documentViewModel.customerFormData.contactEmail)
                .textFieldStyle(CosmicTextFieldStyle())
                .keyboardType(.emailAddress)
                .autocapitalization(.none)
                .multilineTextAlignment(.trailing)

            if let error = documentViewModel.customerFormData.emailError {
                Text(error)
                    .font(.hebrewCaption)
                    .foregroundColor(.destructive)
                    .multilineTextAlignment(.trailing)
            }
        }
    }

    private var phoneField: some View {
        VStack(alignment: .trailing, spacing: .spacing2) {
            Text("טלפון")
                .font(.hebrewBody.weight(.medium))
                .foregroundColor(.cardForeground)

            TextField("050-1234567", text: $documentViewModel.customerFormData.contactPhone)
                .textFieldStyle(CosmicTextFieldStyle())
                .keyboardType(.phonePad)
                .multilineTextAlignment(.trailing)

            if let error = documentViewModel.customerFormData.phoneError {
                Text(error)
                    .font(.hebrewCaption)
                    .foregroundColor(.destructive)
                    .multilineTextAlignment(.trailing)
            }
        }
    }

    private var notesField: some View {
        VStack(alignment: .trailing, spacing: .spacing2) {
            Text("הערות")
                .font(.hebrewBody.weight(.medium))
                .foregroundColor(.cardForeground)

            TextField("הערות נוספות...", text: $documentViewModel.customerFormData.notes)
                .textFieldStyle(CosmicTextFieldStyle())
                .multilineTextAlignment(.trailing)
        }
    }

    private var buttonSection: some View {
        VStack(spacing: .spacing4) {
            // Success message
            if let successMessage = documentViewModel.successMessage {
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)

                    Text(successMessage)
                        .font(.hebrewBody)
                        .foregroundColor(.cardForeground)
                        .multilineTextAlignment(.trailing)
                }
                .padding(.spacing2)
            }

            // Error message
            if let errorMessage = documentViewModel.errorMessage {
                HStack {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .foregroundColor(.destructive)

                    Text(errorMessage)
                        .font(.hebrewBody)
                        .foregroundColor(.destructive)
                        .multilineTextAlignment(.trailing)
                }
                .padding(.spacing2)
            }

            // Buttons
            HStack(spacing: .spacing4) {
                Button("ביטול") {
                    dismiss()
                }
                .buttonStyle(CosmicSecondaryButtonStyle())
                .frame(maxWidth: .infinity)
                .disabled(documentViewModel.isLoading)

                Button(documentViewModel.isLoading ? "מעדכן..." : "עדכן לקוח") {
                    documentViewModel.updateCustomer()
                }
                .buttonStyle(CosmicPrimaryButtonStyle())
                .frame(maxWidth: .infinity)
                .disabled(!documentViewModel.customerFormData.isValid || documentViewModel.isLoading)
            }
        }
        .padding(.top, .spacing4)
    }
}

#Preview("Expenses") {
    ExpensesView()
}

#Preview("Settings") {
    SettingsView()
        .environmentObject(AuthViewModel())
}
