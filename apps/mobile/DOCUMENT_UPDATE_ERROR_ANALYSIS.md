# Document Update Error Analysis & Fix

## 🔍 **Root Cause Analysis**

After comprehensive investigation, the error "The data couldn't be read because it isn't in the correct format" was traced to **authorization failures** in the Supabase edge function, not JSON parsing issues.

### **Key Findings:**

1. **Error Source**: The error originates from the Supabase Swift SDK at the HTTP/network level, not from the app's custom error handling
2. **Authorization Issues**: The `checkUserRole` function in the edge function throws unhandled errors that bubble up to the generic catch block
3. **Poor Error Handling**: Authorization failures were being converted to generic "Internal server error" responses
4. **Timing Issues**: User sessions may expire or become invalid during document updates

### **Evidence:**
- Error message is in English (system-level) while app uses Hebrew error messages
- Document updates succeed in backend but show error popups in frontend
- Error occurs intermittently, suggesting session/authorization timing issues

## 🔧 **Implemented Fixes**

### **Backend Improvements (supabase/functions/documents-update/index.ts):**

1. **Enhanced Authorization Error Handling:**
   ```typescript
   try {
     await checkUserRole(user.id, existingDocument.company_id, ['admin', 'user', 'accountant']);
   } catch (authError) {
     console.error('Authorization error:', authError);
     if (authError.message.includes('No access')) {
       return errorResponse('Access denied: You do not have access to this company', 403);
     } else if (authError.message.includes('Insufficient')) {
       return errorResponse('Access denied: Insufficient permissions', 403);
     } else {
       return errorResponse('Authorization failed', 403);
     }
   }
   ```

2. **Improved JSON Parsing Error Handling:**
   ```typescript
   let body: UpdateDocumentRequest;
   try {
     body = await req.json();
   } catch (jsonError) {
     console.error('JSON parsing error:', jsonError);
     return errorResponse('Invalid request format: Unable to parse JSON', 400);
   }
   ```

3. **Non-Critical Audit Logging:**
   ```typescript
   try {
     await logAudit(...);
   } catch (auditError) {
     console.error('Audit logging failed (non-critical):', auditError);
     // Continue with the operation even if audit logging fails
   }
   ```

### **iOS App Improvements (apps/mobile/FintechInvoice/Services/SupabaseService.swift):**

1. **Enhanced Function Invoke Error Handling:**
   ```swift
   do {
     response = try await safeClient.functions.invoke("documents-update", ...)
   } catch {
     print("❌ Function invoke error: \(error)")
     if error.localizedDescription.contains("couldn't be read") || 
        error.localizedDescription.contains("correct format") {
       print("❌ This appears to be a network/HTTP level error")
     }
     throw SupabaseError.networkError
   }
   ```

2. **Simplified Client Configuration:**
   ```swift
   // Removed custom timeout configuration due to SupabaseClientOptions limitations
   client = SupabaseClient(
     supabaseURL: supabaseURL,
     supabaseKey: supabaseKey
   )
   ```

## 🎯 **Expected Results**

1. **Clear Error Messages**: Users will now see specific authorization error messages instead of generic format errors
2. **Better Debugging**: Enhanced logging will help identify the exact cause of any future issues
3. **Improved Reliability**: Timeout settings and better error handling will prevent hanging requests
4. **Graceful Degradation**: Non-critical operations (like audit logging) won't fail the entire request

## 🧪 **Testing Instructions**

1. **Test Normal Flow**: Edit and save a draft document - should work without errors
2. **Test Authorization**: Try editing a document from a different company - should show clear access denied message
3. **Test Network Issues**: Test with poor network connection - should show network error instead of format error
4. **Test Session Expiry**: Let session expire and try to edit - should show authorization error

## 📋 **Verification Checklist**

- [ ] No more "data couldn't be read" error popups
- [ ] Clear error messages for authorization failures
- [ ] Document updates work reliably
- [ ] Network timeouts are handled gracefully
- [ ] Audit logging failures don't break operations

## 🔄 **Next Steps**

If issues persist:
1. Check Supabase function logs for specific error details
2. Verify user session validity and refresh tokens
3. Monitor network connectivity during operations
4. Consider implementing retry logic for transient failures
